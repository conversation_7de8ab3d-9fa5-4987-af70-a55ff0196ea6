<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import BulkOperationsTable from './BulkOperationsTable.vue';
import BulkOperationDetails from './BulkOperationDetails.vue';
import { useTrpc } from '../../../composables/useTrpc';
import { toast } from 'sonner';
import Button from '@/volt/Button.vue';

const props = defineProps<{
  operationId?: number;
}>();

const trpc = useTrpc();

const currentView = ref<'list' | 'details'>('list');
const selectedOperationId = ref<number | null>(props.operationId || null);

// For list view
const operations = ref<any>(null);
const isLoadingOperations = ref(false);
const operationsError = ref<any>(null);

async function refetchOperations(pagination?: { take: number; skip: number }, filters?: { status: any; search: string }) {
    isLoadingOperations.value = true;
    operationsError.value = null;
    try {
        const input = {
            take: pagination?.take ?? 20,
            skip: pagination?.skip ?? 0,
            status: filters?.status,
            search: filters?.search,
        };
        const result: any = await trpc.matching.listBulkOperations(input);
        if (result) {
          operations.value = result;
        }
    } catch (e) {
        operationsError.value = e;
    } finally {
        isLoadingOperations.value = false;
    }
}

// For details view
const operationDetails = ref<any>(null);
const isLoadingDetails = ref(false);
const detailsError = ref<any>(null);

async function fetchDetails() {
    if (!selectedOperationId.value) return;
    isLoadingDetails.value = true;
    detailsError.value = null;
    try {
        operationDetails.value = await trpc.matching.getBulkOperationReport({ operationId: selectedOperationId.value });
    } catch (e) {
        detailsError.value = e;
    } finally {
        isLoadingDetails.value = false;
    }
}

// For export
const isExporting = ref(false);
async function exportReport(input: { operationId: number, format: 'CSV' | 'JSON' }) {
    isExporting.value = true;
    try {
        const result = await trpc.matching.exportBulkOperationReport(input);
        // Logic to handle file download would be here
        toast.success('Отчет успешно экспортирован');
        return result;
    } catch (e) {
        toast.error('Ошибка экспорта отчета');
    }
    finally {
        isExporting.value = false;
    }
}

const handleViewDetails = (operationId: number) => {
  selectedOperationId.value = operationId;
  currentView.value = 'details';
};

const handleBackToList = () => {
  selectedOperationId.value = null;
  currentView.value = 'list';
  operationDetails.value = null; // Clear details
  refetchOperations();
};

const handleRollbackSuccess = () => {
  toast.success('Операция успешно отменена', {
    description: 'Данные были возвращены в исходное состояние.',
  });
  handleBackToList();
};

watch(
  () => props.operationId,
  (newId) => {
    if (newId) {
      selectedOperationId.value = newId;
      currentView.value = 'details';
    }
  },
);

onMounted(() => {
  const urlOpId = new URLSearchParams(window.location.search).get('operationId');
  if (urlOpId) {
    const opId = Number(urlOpId);
    if (!isNaN(opId)) {
      selectedOperationId.value = opId;
      currentView.value = 'details';
      fetchDetails(); // Fetch details if opened directly
      return;
    }
  }
  
  if (props.operationId) {
    selectedOperationId.value = props.operationId;
    currentView.value = 'details';
    fetchDetails(); // Fetch details if passed as prop
  } else {
    refetchOperations();
  }
});

watch(selectedOperationId, (newId, oldId) => {
  if (newId && newId !== oldId && currentView.value === 'details') {
    fetchDetails();
  }
});

</script>

<template>
  <div>
    <div v-if="currentView === 'list'">
      <h2 class="text-2xl font-bold mb-4">Отчеты о массовых операциях</h2>
      <div v-if="isLoadingOperations">Загрузка...</div>
      <div v-else-if="operationsError">Ошибка: {{ operationsError.message }}</div>
      <BulkOperationsTable
        v-else-if="operations"
        :operations="operations.items"
        :total="operations.total"
        :loading="isLoadingOperations"
        @view-details="handleViewDetails"
        @update:pagination="refetchOperations($event, undefined)"
        @update:filters="refetchOperations(undefined, $event)"
        @refetch="refetchOperations()"
      />
    </div>
    <div v-else-if="currentView === 'details' && selectedOperationId">
      <Button variant="outline" @click="handleBackToList" class="mb-4">
        &larr; Назад к списку
      </Button>
      <div v-if="isLoadingDetails">Загрузка деталей...</div>
      <div v-else-if="detailsError">Ошибка: {{ detailsError.message }}</div>
      <BulkOperationDetails
        v-else-if="operationDetails"
        :operation-id="selectedOperationId"
        @rollback-success="handleRollbackSuccess"
      />
    </div>
  </div>
</template>